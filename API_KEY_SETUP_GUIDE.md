# API Key Setup Guide

This guide explains how users can input their API key in the setup page and how it gets sent to the Pi device.

## Overview

The API key functionality allows users to:
- ✅ Input their API key directly in the setup page
- ✅ Validate API key format in real-time
- ✅ Save API key securely to local storage
- ✅ Automatically send API key to Pi device
- ✅ View masked API key for security
- ✅ Clear API key when needed

## Features

### 1. API Key Input Section
Located in the setup page (`lib/screens/setup_page.dart`), users can:
- Enter their API key in a secure text field
- Toggle visibility of the API key
- See real-time validation feedback
- Save the API key to local storage

### 2. API Key Validation
The system validates API keys using multiple criteria:
- **Minimum length**: 16 characters
- **Valid characters**: Alphanumeric, hyphens, underscores only
- **Common patterns**:
  - OpenAI keys: `sk-` prefix, 48+ characters
  - Generic API keys: `api_` prefix, 20+ characters
  - Custom keys: `key_` prefix, 20+ characters
  - Standard keys: 32+ characters

### 3. Secure Storage
API keys are stored using `SharedPreferences` with:
- Encrypted local storage
- Validation status tracking
- Easy retrieval and clearing

### 4. Pi Communication
When sharing credentials, the system:
- Validates the API key format
- Sends user credentials + API key to Pi
- Handles connection errors gracefully
- Provides user feedback

## Usage Flow

### Step 1: User Input
1. User opens the setup page
2. Scrolls to "API Key Configuration" section
3. Enters their API key in the text field
4. Sees real-time validation feedback

### Step 2: Save API Key
1. User clicks "Save API Key" button
2. System validates the key format
3. Key is saved to local storage
4. Success message is displayed

### Step 3: Share with Pi
1. User clicks "Share Credentials with Immy"
2. System retrieves the saved API key
3. Validates the key one more time
4. Sends credentials to Pi at `***********:80/save_creds`
5. Displays success/error message

## API Key Service (`lib/services/api_key_service.dart`)

### Key Methods

```dart
// Validate API key format
bool isValidApiKey(String apiKey)

// Save API key to storage
Future<bool> saveApiKey(String apiKey)

// Get API key from storage
Future<String?> getApiKey()

// Send credentials to Pi
Future<Map<String, dynamic>> sendCredentialsToPi({
  required String apiKey,
  required String username,
  String? userEmail,
  int? userId,
})

// Test Pi connection
Future<bool> testPiConnection()
```

### Supported API Key Formats

The system recognizes these common API key patterns:

1. **OpenAI Format**: `sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
2. **Generic API**: `api_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
3. **Custom Key**: `key_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`
4. **Standard**: `xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

## Pi Communication Protocol

### Endpoint
- **URL**: `http://***********:80/save_creds`
- **Method**: POST
- **Content-Type**: application/json

### Request Format
```json
{
  "username": "user_name",
  "api_key": "user_api_key",
  "email": "<EMAIL>",
  "user_id": "123",
  "timestamp": "2024-08-05T10:30:00.000Z",
  "app_version": "1.0.0"
}
```

### Response Handling
- **Success (200)**: Credentials saved successfully
- **Error (4xx/5xx)**: Display error message to user
- **Network Error**: Show connection failure message

## Security Features

### 1. API Key Masking
- Display format: `sk-1234****************************cdef`
- Shows first 4 and last 4 characters
- Masks middle characters with asterisks

### 2. Input Validation
- Real-time format checking
- Character validation (alphanumeric + `-` + `_`)
- Length requirements
- Pattern matching

### 3. Secure Storage
- Uses Flutter's SharedPreferences
- Keys are stored locally on device
- No cloud storage or external transmission (except to Pi)

## Error Handling

### Common Error Messages
- "Please enter an API key" - Empty input
- "Invalid API key format" - Format validation failed
- "Failed to connect to Immy" - Network connection error
- "Pi responded with status: XXX" - Pi server error

### User Feedback
- ✅ Green checkmark for valid keys
- ❌ Red error text for invalid keys
- 🔄 Loading spinner during validation
- 📱 Snackbar messages for actions

## Testing

### Run API Key Tests
```bash
flutter test test/api_key_service_test.dart
```

### Test Coverage
- ✅ API key format validation
- ✅ Storage operations (save/get/clear)
- ✅ Validation status tracking
- ✅ Masked display functionality
- ✅ Error handling scenarios
- ✅ Complete workflow integration

## Troubleshooting

### Common Issues

1. **API Key Not Saving**
   - Check key format meets requirements
   - Ensure minimum 16 characters
   - Verify only valid characters used

2. **Pi Connection Failed**
   - Verify Pi is powered on
   - Check Wi-Fi connection to Pi network
   - Confirm Pi IP address (***********)

3. **Invalid Format Error**
   - Review supported formats above
   - Remove any spaces or special characters
   - Ensure key meets minimum length

### Debug Information
- Enable debug mode to see detailed logs
- Check console for API key validation messages
- Monitor network requests to Pi

## Future Enhancements

Potential improvements:
- [ ] Support for multiple API key types
- [ ] Cloud backup of encrypted keys
- [ ] API key expiration tracking
- [ ] Integration with key management services
- [ ] Batch credential sharing
- [ ] QR code scanning for key input

## Support

For additional help:
- Check the setup page UI for format hints
- Review error messages for specific guidance
- Test Pi connection using the built-in test function
- Verify API key with your service provider
